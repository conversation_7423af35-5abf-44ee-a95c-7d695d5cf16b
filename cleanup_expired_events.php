<?php
/**
 * ملف تنظيف الفعاليات المنتهية
 * يتم تشغيله لحذف الفعاليات المنتهية وتحديث حالة المواصلات المرتبطة
 */

require_once 'includes/init.php';

// إنشاء اتصال قاعدة البيانات
$db = new Database();

// دالة لإرسال إشعار للإدمن
function sendAdminNotification($message, $type = 'info') {
    global $db;
    
    try {
        $db->query("
            INSERT INTO admin_notifications (message, type, created_at) 
            VALUES (:message, :type, NOW())
        ");
        $db->bind(':message', $message);
        $db->bind(':type', $type);
        $db->execute();
    } catch (Exception $e) {
        error_log("فشل في إرسال الإشعار: " . $e->getMessage());
    }
}

// دالة لحذف الفعاليات المنتهية
function cleanupExpiredEvents() {
    global $db;
    
    try {
        echo "🔍 البحث عن الفعاليات المنتهية...\n";
        
        // جلب الفعاليات المنتهية (التي انتهت منذ أكثر من ساعة)
        $db->query("
            SELECT 
                e.id,
                e.title,
                e.date_time,
                e.end_time,
                COUNT(DISTINCT t.id) as tickets_count,
                COUNT(DISTINCT tb.id) as transport_bookings_count,
                COUNT(DISTINCT tt.id) as transport_trips_count
            FROM events e
            LEFT JOIN tickets t ON e.id = t.event_id
            LEFT JOIN transport_bookings tb ON e.id = tb.event_id
            LEFT JOIN transport_trips tt ON e.id = tt.event_id
            WHERE (e.end_time < DATE_SUB(NOW(), INTERVAL 1 HOUR) OR 
                   (e.end_time IS NULL AND e.date_time < DATE_SUB(NOW(), INTERVAL 1 HOUR)))
            AND e.status = 'active'
            GROUP BY e.id
        ");
        $expired_events = $db->resultSet();
        
        if (empty($expired_events)) {
            echo "✅ لا توجد فعاليات منتهية للمعالجة\n";
            return 0;
        }
        
        echo "📋 تم العثور على " . count($expired_events) . " فعالية منتهية\n\n";
        
        $processed_count = 0;
        $total_tickets_deleted = 0;
        $total_transport_bookings_deleted = 0;
        $total_transport_trips_updated = 0;
        
        foreach ($expired_events as $event) {
            echo "🎭 معالجة الفعالية: {$event['title']}\n";
            echo "   📅 تاريخ الانتهاء: " . ($event['end_time'] ?: $event['date_time']) . "\n";
            
            // تحديث حالة رحلات المواصلات إلى "منتهية" بدلاً من حذفها
            if ($event['transport_trips_count'] > 0) {
                $db->query("
                    UPDATE transport_trips 
                    SET status = 'expired', 
                        is_active = 0,
                        updated_at = NOW()
                    WHERE event_id = :event_id
                ");
                $db->bind(':event_id', $event['id']);
                $db->execute();
                $total_transport_trips_updated += $event['transport_trips_count'];
                echo "   🚌 تم تحديث {$event['transport_trips_count']} رحلة مواصلات إلى حالة 'منتهية'\n";
            }
            
            // حذف حجوزات المواصلات المرتبطة
            if ($event['transport_bookings_count'] > 0) {
                $db->query("DELETE FROM transport_bookings WHERE event_id = :event_id");
                $db->bind(':event_id', $event['id']);
                $db->execute();
                $total_transport_bookings_deleted += $event['transport_bookings_count'];
                echo "   🗑️ تم حذف {$event['transport_bookings_count']} حجز مواصلات\n";
            }
            
            // حذف التذاكر المرتبطة
            if ($event['tickets_count'] > 0) {
                $db->query("DELETE FROM tickets WHERE event_id = :event_id");
                $db->bind(':event_id', $event['id']);
                $db->execute();
                $total_tickets_deleted += $event['tickets_count'];
                echo "   🎫 تم حذف {$event['tickets_count']} تذكرة\n";
            }
            
            // تحديث حالة الفعالية إلى منتهية بدلاً من حذفها
            $db->query("
                UPDATE events 
                SET status = 'expired', 
                    is_active = 0,
                    updated_at = NOW()
                WHERE id = :event_id
            ");
            $db->bind(':event_id', $event['id']);
            
            if ($db->execute()) {
                $processed_count++;
                echo "   ✅ تم تحديث حالة الفعالية إلى 'منتهية'\n";
                
                // إرسال إشعار للإدمن
                $message = "تم معالجة فعالية منتهية: {$event['title']}\n";
                $message .= "تاريخ الانتهاء: " . ($event['end_time'] ?: $event['date_time']) . "\n";
                if ($event['tickets_count'] > 0 || $event['transport_bookings_count'] > 0 || $event['transport_trips_count'] > 0) {
                    $message .= "تم حذف {$event['tickets_count']} تذكرة و {$event['transport_bookings_count']} حجز مواصلات\n";
                    $message .= "تم تحديث {$event['transport_trips_count']} رحلة مواصلات إلى حالة منتهية";
                }
                sendAdminNotification($message, 'info');
            }
            echo "\n";
        }
        
        // إرسال ملخص عام
        if ($processed_count > 0) {
            $summary = "تم معالجة {$processed_count} فعالية منتهية تلقائياً\n";
            $summary .= "- تم حذف {$total_tickets_deleted} تذكرة\n";
            $summary .= "- تم حذف {$total_transport_bookings_deleted} حجز مواصلات\n";
            $summary .= "- تم تحديث {$total_transport_trips_updated} رحلة مواصلات إلى حالة منتهية";
            
            sendAdminNotification($summary, 'success');
            echo "📊 ملخص العملية:\n";
            echo "   ✅ تم معالجة {$processed_count} فعالية\n";
            echo "   🎫 تم حذف {$total_tickets_deleted} تذكرة\n";
            echo "   🚌 تم حذف {$total_transport_bookings_deleted} حجز مواصلات\n";
            echo "   🔄 تم تحديث {$total_transport_trips_updated} رحلة مواصلات\n";
        }
        
        return $processed_count;
        
    } catch (Exception $e) {
        $error_message = "خطأ في معالجة الفعاليات المنتهية: " . $e->getMessage();
        sendAdminNotification($error_message, 'error');
        echo "❌ خطأ: " . $error_message . "\n";
        return 0;
    }
}

// تشغيل العملية
echo "🚀 بدء عملية تنظيف الفعاليات المنتهية...\n";
echo "⏰ الوقت: " . date('Y-m-d H:i:s') . "\n\n";

$processed = cleanupExpiredEvents();

echo "\n🏁 انتهت العملية. تم معالجة {$processed} فعالية.\n";
echo "⏰ وقت الانتهاء: " . date('Y-m-d H:i:s') . "\n";
?>
