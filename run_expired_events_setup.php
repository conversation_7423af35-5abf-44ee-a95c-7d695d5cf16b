<?php
/**
 * ملف تشغيل سريع لإعداد نظام الفعاليات المنتهية
 * يقوم بتشغيل جميع العمليات المطلوبة
 */

echo "🚀 بدء إعداد نظام الفعاليات المنتهية الشامل...\n";
echo "⏰ الوقت: " . date('Y-m-d H:i:s') . "\n\n";

// 1. إعداد قاعدة البيانات
echo "📊 الخطوة 1: إعداد قاعدة البيانات...\n";
echo "=" . str_repeat("=", 50) . "\n";
include 'setup_expired_events_system.php';

echo "\n\n";

// 2. تنظيف الفعاليات المنتهية
echo "🧹 الخطوة 2: تنظيف الفعاليات المنتهية...\n";
echo "=" . str_repeat("=", 50) . "\n";
include 'cleanup_expired_events.php';

echo "\n\n";

echo "🎉 تم إكمال إعداد نظام الفعاليات المنتهية بنجاح!\n";
echo "⏰ وقت الانتهاء: " . date('Y-m-d H:i:s') . "\n\n";

echo "📋 ملخص التحديثات المطبقة:\n";
echo "   ✅ تم إضافة أعمدة status للجداول\n";
echo "   ✅ تم تحديث الفعاليات المنتهية\n";
echo "   ✅ تم تحديث رحلات المواصلات المنتهية\n";
echo "   ✅ تم إنشاء الفهارس لتحسين الأداء\n";
echo "   ✅ تم تنظيف البيانات المنتهية\n\n";

echo "🔧 الميزات الجديدة:\n";
echo "   📱 صفحة events.php تستبعد الفعاليات المنتهية تلقائياً\n";
echo "   🎫 صفحة my-tickets.php تظهر التذاكر المنتهية بوضوح\n";
echo "   🚌 حجوزات المواصلات تظهر حالة 'منتهية' للرحلات المنتهية\n";
echo "   🗑️ ملف cleanup_expired_events.php لتنظيف دوري\n";
echo "   ⚙️ دالة auto_cleanup_expired_events() للتنظيف التلقائي\n\n";

echo "📝 ملاحظات مهمة:\n";
echo "   - يمكن تشغيل cleanup_expired_events.php دورياً عبر cron job\n";
echo "   - التنظيف التلقائي يحدث عند زيارة events.php\n";
echo "   - الفعاليات المنتهية تُحدث حالتها بدلاً من الحذف\n";
echo "   - رحلات المواصلات المنتهية تظهر كـ 'منتهية' وليس محذوفة\n\n";

echo "🎯 الخطوات التالية الموصى بها:\n";
echo "   1. اختبار النظام بإضافة فعالية منتهية\n";
echo "   2. التحقق من عرض الحالات في my-tickets.php\n";
echo "   3. إعداد cron job لتشغيل cleanup_expired_events.php يومياً\n";
echo "   4. مراقبة الإشعارات في لوحة تحكم المواصلات\n\n";

echo "✨ تم الانتهاء من جميع العمليات بنجاح!\n";
?>
