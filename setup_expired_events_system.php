<?php
/**
 * ملف إعداد نظام الفعاليات المنتهية
 * يقوم بتحديث قاعدة البيانات وإضافة الأعمدة المطلوبة
 */

require_once 'includes/init.php';

// إنشاء اتصال قاعدة البيانات
$db = new Database();

echo "🚀 بدء إعداد نظام الفعاليات المنتهية...\n\n";

try {
    // التحقق من وجود عمود status في جدول events
    echo "🔍 التحقق من جدول events...\n";
    $db->query("SHOW COLUMNS FROM events LIKE 'status'");
    $events_status_exists = $db->single();
    
    if (!$events_status_exists) {
        echo "➕ إضافة عمود status لجدول events...\n";
        $db->query("ALTER TABLE `events` ADD COLUMN `status` ENUM('active', 'expired', 'cancelled', 'postponed') DEFAULT 'active' AFTER `is_active`");
        $db->execute();
        echo "✅ تم إضافة عمود status لجدول events\n";
    } else {
        echo "✅ عمود status موجود في جدول events\n";
    }
    
    // التحقق من وجود عمود status في جدول transport_trips
    echo "🔍 التحقق من جدول transport_trips...\n";
    $db->query("SHOW COLUMNS FROM transport_trips LIKE 'status'");
    $trips_status_exists = $db->single();
    
    if (!$trips_status_exists) {
        echo "➕ إضافة عمود status لجدول transport_trips...\n";
        $db->query("ALTER TABLE `transport_trips` ADD COLUMN `status` ENUM('active', 'expired', 'cancelled') DEFAULT 'active' AFTER `is_active`");
        $db->execute();
        echo "✅ تم إضافة عمود status لجدول transport_trips\n";
    } else {
        echo "✅ عمود status موجود في جدول transport_trips\n";
    }
    
    // تحديث الفعاليات الموجودة لتعيين الحالة الصحيحة
    echo "🔄 تحديث حالة الفعاليات الموجودة...\n";
    $db->query("
        UPDATE `events` 
        SET `status` = 'expired', `is_active` = 0
        WHERE (end_time < NOW() OR (end_time IS NULL AND date_time < NOW())) 
        AND `status` = 'active'
    ");
    $updated_events = $db->execute();
    echo "✅ تم تحديث حالة {$updated_events} فعالية منتهية\n";
    
    // تحديث رحلات المواصلات المرتبطة بالفعاليات المنتهية
    echo "🚌 تحديث حالة رحلات المواصلات...\n";
    $db->query("
        UPDATE `transport_trips` tt
        JOIN `events` e ON tt.event_id = e.id
        SET tt.status = 'expired', tt.is_active = 0
        WHERE e.status = 'expired' AND tt.status = 'active'
    ");
    $updated_trips = $db->execute();
    echo "✅ تم تحديث حالة {$updated_trips} رحلة مواصلات\n";
    
    // إنشاء الفهارس لتحسين الأداء
    echo "📊 إنشاء الفهارس لتحسين الأداء...\n";
    
    try {
        $db->query("CREATE INDEX idx_events_status_date ON events(status, date_time)");
        $db->execute();
        echo "✅ تم إنشاء فهرس events\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "ℹ️ فهرس events موجود مسبقاً\n";
        } else {
            throw $e;
        }
    }
    
    try {
        $db->query("CREATE INDEX idx_transport_trips_status ON transport_trips(status, event_id)");
        $db->execute();
        echo "✅ تم إنشاء فهرس transport_trips\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "ℹ️ فهرس transport_trips موجود مسبقاً\n";
        } else {
            throw $e;
        }
    }
    
    echo "\n🎉 تم إعداد نظام الفعاليات المنتهية بنجاح!\n";
    echo "📋 ملخص العملية:\n";
    echo "   - تم تحديث {$updated_events} فعالية منتهية\n";
    echo "   - تم تحديث {$updated_trips} رحلة مواصلات\n";
    echo "   - تم إنشاء الفهارس المطلوبة\n\n";
    
    echo "🔧 الخطوات التالية:\n";
    echo "   1. يمكنك الآن تشغيل cleanup_expired_events.php لتنظيف الفعاليات المنتهية\n";
    echo "   2. سيتم تنظيف الفعاليات المنتهية تلقائياً عند زيارة صفحة events.php\n";
    echo "   3. رحلات المواصلات المرتبطة بالفعاليات المنتهية ستظهر كـ 'منتهية'\n\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في إعداد النظام: " . $e->getMessage() . "\n";
    echo "💡 تأكد من أن قاعدة البيانات متصلة وأن لديك صلاحيات التعديل\n";
}

echo "⏰ انتهت العملية في: " . date('Y-m-d H:i:s') . "\n";
?>
