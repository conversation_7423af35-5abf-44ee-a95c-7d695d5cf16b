<?php
require_once '../../includes/init.php';

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

$action = $_POST['action'] ?? '';
$db = new Database();

try {
    switch ($action) {
        case 'delete':
            $id = (int)($_POST['id'] ?? 0);
            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }

            // التحقق من وجود حجوزات مرتبطة بهذه الرحلة
            $db->query("SELECT COUNT(*) as count FROM transport_bookings WHERE trip_id = :id");
            $db->bind(':id', $id);
            $result = $db->single();
            
            if ($result['count'] > 0) {
                throw new Exception('لا يمكن حذف الرحلة لأنها تحتوي على حجوزات');
            }

            // حذف الرحلة
            $db->query("DELETE FROM transport_trips WHERE id = :id");
            $db->bind(':id', $id);
            
            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'تم حذف الرحلة بنجاح']);
            } else {
                throw new Exception('فشل في حذف الرحلة');
            }
            break;

        case 'update':
            $id = (int)($_POST['id'] ?? 0);
            $event_id = (int)($_POST['event_id'] ?? 0);
            $starting_point_id = (int)($_POST['starting_point_id'] ?? 0);
            $driver_id = !empty($_POST['driver_id']) ? (int)$_POST['driver_id'] : null;

            // جلب vehicle_id و transport_type_id من driver_id
            $vehicle_id = null;
            $transport_type_id = null;
            if ($driver_id) {
                $db->query("SELECT v.id, v.transport_type_id FROM transport_vehicles v WHERE v.driver_id = :driver_id LIMIT 1");
                $db->bind(':driver_id', $driver_id);
                $vehicle_result = $db->single();
                if ($vehicle_result) {
                    $vehicle_id = $vehicle_result['id'];
                    $transport_type_id = $vehicle_result['transport_type_id'];
                }
            }
            $departure_time = trim($_POST['departure_time'] ?? '');
            $arrival_time = trim($_POST['arrival_time'] ?? '');
            $price = (float)($_POST['price'] ?? 0);
            $total_seats = (int)($_POST['total_seats'] ?? 0);
            $description = trim($_POST['description'] ?? '');
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }
            if ($event_id <= 0) {
                throw new Exception('الفعالية مطلوبة');
            }
            if ($starting_point_id <= 0) {
                throw new Exception('نقطة الانطلاق مطلوبة');
            }
            if ($driver_id && !$transport_type_id) {
                throw new Exception('لا يمكن العثور على نوع المركبة للسائق المحدد');
            }
            if (empty($departure_time)) {
                throw new Exception('وقت المغادرة مطلوب');
            }

            // التحقق من أن موعد انطلاق الرحلة لا يكون قبل موعد الفعالية
            $db->query("SELECT date_time FROM events WHERE id = :event_id");
            $db->bind(':event_id', $event_id);
            $event = $db->single();

            if ($event && strtotime($departure_time) < strtotime($event['date_time'])) {
                throw new Exception('لا يمكن أن يكون موعد انطلاق الرحلة قبل موعد الفعالية');
            }

            if ($price <= 0) {
                throw new Exception('السعر يجب أن يكون أكبر من صفر');
            }
            if ($total_seats <= 0) {
                throw new Exception('عدد المقاعد يجب أن يكون أكبر من صفر');
            }

            // الحصول على عدد المقاعد المحجوزة
            $db->query("SELECT COALESCE(SUM(passengers_count), 0) as booked_seats FROM transport_bookings WHERE trip_id = :id AND status != 'cancelled'");
            $db->bind(':id', $id);
            $booked_result = $db->single();
            $booked_seats = $booked_result['booked_seats'] ?? 0;
            
            $available_seats = $total_seats - $booked_seats;
            if ($available_seats < 0) {
                throw new Exception('عدد المقاعد الجديد أقل من المقاعد المحجوزة بالفعل (' . $booked_seats . ')');
            }

            // تحديث الرحلة
            $db->query("
                UPDATE transport_trips
                SET event_id = :event_id, starting_point_id = :starting_point_id,
                    transport_type_id = :transport_type_id, driver_id = :driver_id, vehicle_id = :vehicle_id,
                    departure_time = :departure_time, arrival_time = :arrival_time,
                    price = :price, total_seats = :total_seats,
                    available_seats = :available_seats, description = :description,
                    is_active = :is_active, updated_at = NOW()
                WHERE id = :id
            ");
            $db->bind(':event_id', $event_id);
            $db->bind(':starting_point_id', $starting_point_id);
            $db->bind(':transport_type_id', $transport_type_id);
            $db->bind(':driver_id', $driver_id);
            $db->bind(':vehicle_id', $vehicle_id);
            $db->bind(':departure_time', $departure_time);
            $db->bind(':arrival_time', $arrival_time);
            $db->bind(':price', $price);
            $db->bind(':total_seats', $total_seats);
            $db->bind(':available_seats', $available_seats);
            $db->bind(':description', $description);
            $db->bind(':is_active', $is_active);
            $db->bind(':id', $id);
            
            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'تم تحديث الرحلة بنجاح']);
            } else {
                throw new Exception('فشل في تحديث الرحلة');
            }
            break;

        case 'add':
            $event_id = (int)($_POST['event_id'] ?? 0);
            $starting_point_id = (int)($_POST['starting_point_id'] ?? 0);
            $driver_id = !empty($_POST['driver_id']) ? (int)$_POST['driver_id'] : null;

            // جلب vehicle_id و transport_type_id من driver_id
            $vehicle_id = null;
            $transport_type_id = null;
            if ($driver_id) {
                $db->query("SELECT v.id, v.transport_type_id FROM transport_vehicles v WHERE v.driver_id = :driver_id LIMIT 1");
                $db->bind(':driver_id', $driver_id);
                $vehicle_result = $db->single();
                if ($vehicle_result) {
                    $vehicle_id = $vehicle_result['id'];
                    $transport_type_id = $vehicle_result['transport_type_id'];
                }
            }

            $departure_time = trim($_POST['departure_time'] ?? '');
            $arrival_time = trim($_POST['arrival_time'] ?? '');
            $price = (float)($_POST['price'] ?? 0);
            $total_seats = (int)($_POST['total_seats'] ?? 0);
            $description = trim($_POST['description'] ?? '');
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            if ($event_id <= 0) {
                throw new Exception('الفعالية مطلوبة');
            }
            if ($starting_point_id <= 0) {
                throw new Exception('نقطة الانطلاق مطلوبة');
            }
            if ($driver_id && !$transport_type_id) {
                throw new Exception('لا يمكن العثور على نوع المركبة للسائق المحدد');
            }
            if (empty($departure_time)) {
                throw new Exception('وقت المغادرة مطلوب');
            }

            // التحقق من أن موعد انطلاق الرحلة لا يكون قبل موعد الفعالية
            $db->query("SELECT date_time FROM events WHERE id = :event_id");
            $db->bind(':event_id', $event_id);
            $event = $db->single();

            if ($event && strtotime($departure_time) < strtotime($event['date_time'])) {
                throw new Exception('لا يمكن أن يكون موعد انطلاق الرحلة قبل موعد الفعالية');
            }

            if ($price <= 0) {
                throw new Exception('السعر يجب أن يكون أكبر من صفر');
            }
            if ($total_seats <= 0) {
                throw new Exception('عدد المقاعد يجب أن يكون أكبر من صفر');
            }

            // إضافة رحلة جديدة
            $db->query("
                INSERT INTO transport_trips (event_id, starting_point_id, transport_type_id, driver_id, vehicle_id,
                                           departure_time, arrival_time, price, total_seats,
                                           available_seats, description, is_active, created_at, updated_at)
                VALUES (:event_id, :starting_point_id, :transport_type_id, :driver_id, :vehicle_id,
                        :departure_time, :arrival_time, :price, :total_seats,
                        :available_seats, :description, :is_active, NOW(), NOW())
            ");
            $db->bind(':event_id', $event_id);
            $db->bind(':starting_point_id', $starting_point_id);
            $db->bind(':transport_type_id', $transport_type_id);
            $db->bind(':driver_id', $driver_id);
            $db->bind(':vehicle_id', $vehicle_id);
            $db->bind(':departure_time', $departure_time);
            $db->bind(':arrival_time', $arrival_time);
            $db->bind(':price', $price);
            $db->bind(':total_seats', $total_seats);
            $db->bind(':available_seats', $total_seats); // جميع المقاعد متاحة في البداية
            $db->bind(':description', $description);
            $db->bind(':is_active', $is_active);
            
            if ($db->execute()) {
                echo json_encode(['success' => true, 'message' => 'تم إضافة الرحلة بنجاح']);
            } else {
                throw new Exception('فشل في إضافة الرحلة');
            }
            break;

        case 'get':
            $id = (int)($_POST['id'] ?? 0);
            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }

            $db->query("
                SELECT
                    tt.*,
                    tsp.name as starting_point_name,
                    tty.name as transport_type_name,
                    e.title as event_title,
                    d.name as driver_name
                FROM transport_trips tt
                LEFT JOIN transport_starting_points tsp ON tt.starting_point_id = tsp.id
                LEFT JOIN transport_types tty ON tt.transport_type_id = tty.id
                LEFT JOIN events e ON tt.event_id = e.id
                LEFT JOIN transport_drivers d ON tt.driver_id = d.id
                WHERE tt.id = :id
            ");
            $db->bind(':id', $id);
            $trip = $db->single();
            
            if (!$trip) {
                throw new Exception('الرحلة غير موجودة');
            }

            echo json_encode(['success' => true, 'data' => $trip]);
            break;

        case 'toggle_status':
            $id = (int)($_POST['id'] ?? 0);
            $new_status = $_POST['new_status'] === 'true' ? 1 : 0;

            if ($id <= 0) {
                throw new Exception('معرف غير صحيح');
            }

            // تحديث حالة الرحلة
            $db->query("UPDATE transport_trips SET is_active = :status, updated_at = NOW() WHERE id = :id");
            $db->bind(':status', $new_status);
            $db->bind(':id', $id);

            if ($db->execute()) {
                $status_text = $new_status ? 'نشط' : 'غير نشط';
                echo json_encode(['success' => true, 'message' => 'تم تغيير حالة الرحلة إلى: ' . $status_text]);
            } else {
                throw new Exception('فشل في تغيير حالة الرحلة');
            }
            break;

        case 'get_inactive':
            // جلب الرحلات غير النشطة
            $db->query("
                SELECT
                    tt.*,
                    tsp.name as starting_point_name,
                    tty.name as transport_type_name,
                    e.title as event_title,
                    e.date_time as event_date
                FROM transport_trips tt
                LEFT JOIN transport_starting_points tsp ON tt.starting_point_id = tsp.id
                LEFT JOIN transport_types tty ON tt.transport_type_id = tty.id
                LEFT JOIN events e ON tt.event_id = e.id
                WHERE tt.is_active = 0
                ORDER BY tt.departure_time DESC
            ");
            $trips = $db->resultSet();

            echo json_encode(['success' => true, 'trips' => $trips]);
            break;

        default:
            throw new Exception('عملية غير مدعومة');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
