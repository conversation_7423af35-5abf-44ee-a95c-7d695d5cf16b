# إعداد Cron Jobs لنظام المواصلات

## لتشغيل المهام المجدولة كل ساعة:
0 * * * * /usr/bin/php /path/to/your/project/transport/scheduled_tasks.php

## لتشغيل المهام المجدولة كل 30 دقيقة:
*/30 * * * * /usr/bin/php /path/to/your/project/transport/scheduled_tasks.php

## لتشغيل المهام المجدولة يومياً في الساعة 2:00 صباحاً:
0 2 * * * /usr/bin/php /path/to/your/project/transport/scheduled_tasks.php

## لتشغيل المهام المجدولة عبر wget (إذا كان PHP CLI غير متاح):
0 * * * * /usr/bin/wget -q -O /dev/null "http://yourdomain.com/transport/scheduled_tasks.php?run_tasks=1"

## لتشغيل المهام المجدولة عبر curl:
0 * * * * /usr/bin/curl -s "http://yourdomain.com/transport/scheduled_tasks.php?run_tasks=1" > /dev/null

## تعليمات الإعداد:

1. افتح terminal أو SSH إلى الخادم
2. اكتب الأمر: crontab -e
3. أضف أحد الأسطر أعلاه (اختر المناسب لبيئتك)
4. احفظ واخرج من المحرر
5. تحقق من إعداد cron بالأمر: crontab -l

## ملاحظات مهمة:

- استبدل "/path/to/your/project" بالمسار الفعلي لمشروعك
- استبدل "yourdomain.com" بالدومين الفعلي لموقعك
- تأكد من أن PHP CLI مثبت ومتاح في المسار المحدد
- يمكنك تعديل التوقيت حسب احتياجاتك

## لاختبار المهام يدوياً:

# عبر PHP CLI:
php /path/to/your/project/transport/scheduled_tasks.php

# عبر المتصفح:
http://yourdomain.com/transport/scheduled_tasks.php?run_tasks=1

## مراقبة السجلات:

يمكنك مراقبة سجلات الأخطاء في:
- /var/log/cron.log (سجلات cron)
- /var/log/apache2/error.log (سجلات PHP)
- أو في مجلد logs الخاص بمشروعك
