-- تحديث قاعدة البيانات لدعم الفعاليات والمواصلات المنتهية

-- إضافة عمود status لجدول transport_trips إذا لم يكن موجوداً
ALTER TABLE `transport_trips` 
ADD COLUMN `status` ENUM('active', 'expired', 'cancelled') DEFAULT 'active' 
AFTER `is_active`;

-- إضافة عمود status لجدول events إذا لم يكن موجوداً
ALTER TABLE `events` 
ADD COLUMN `status` ENUM('active', 'expired', 'cancelled', 'postponed') DEFAULT 'active' 
AFTER `is_active`;

-- تحديث الفعاليات الموجودة لتعيين الحالة الصحيحة
UPDATE `events` 
SET `status` = 'expired' 
WHERE (end_time < NOW() OR (end_time IS NULL AND date_time < NOW())) 
AND `status` = 'active';

-- تحديث رحلات المواصلات المرتبطة بالفعاليات المنتهية
UPDATE `transport_trips` tt
JOIN `events` e ON tt.event_id = e.id
SET tt.status = 'expired', tt.is_active = 0
WHERE e.status = 'expired' AND tt.status = 'active';

-- إنشاء فهرس لتحسين الأداء
CREATE INDEX idx_events_status_date ON events(status, date_time);
CREATE INDEX idx_transport_trips_status ON transport_trips(status, event_id);
